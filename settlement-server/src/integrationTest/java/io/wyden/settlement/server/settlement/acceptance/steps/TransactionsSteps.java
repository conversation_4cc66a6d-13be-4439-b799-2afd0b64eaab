package io.wyden.settlement.server.settlement.acceptance.steps;

import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.common.Metadata;
import io.wyden.settlement.server.settlement.acceptance.SharedTestState;
import io.wyden.settlement.server.settlement.transaction.FeeMapper;
import io.wyden.settlement.server.settlement.transaction.StreetCashTradeMapper;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

public class TransactionsSteps {
    private Logger LOGGER = LoggerFactory.getLogger(TransactionsSteps.class);

    private final TransactionRepository transactionRepository;
    private final TransactionFeeRepository transactionFeeRepository;

    public TransactionsSteps(TransactionRepository transactionRepository, TransactionFeeRepository transactionFeeRepository) {
        this.transactionRepository = transactionRepository;
        this.transactionFeeRepository = transactionFeeRepository;
    }

    @Given("Assume following transaction")
    public void streetCashTrade(DataTable dataTable) {
        Map<String, String> variables = dataTable.asMap();

        String uuid = variables.getOrDefault("uuid", "");
        StreetCashTradeSnapshot streetCashTrade = StreetCashTradeSnapshot.newBuilder()
            .setOrderId(variables.getOrDefault("orderId", ""))
            .setUuid(uuid)
            .setPortfolio(variables.getOrDefault("portfolio", ""))
            .setCurrency(variables.getOrDefault("currency", ""))
            .setQuantity(variables.getOrDefault("quantity", "0"))
            .setPrice(variables.getOrDefault("price", "0"))
            .setLeavesQuantity(variables.getOrDefault("leavesQuantity", "0"))
            .setVenueExecutionId(variables.getOrDefault("venueExecutionId", ""))
            .setDescription(variables.getOrDefault("description", ""))
            .setIntOrderId(variables.getOrDefault("intOrderId", ""))
            .setExtOrderId(variables.getOrDefault("extOrderId", ""))
            .setBaseCurrency(variables.getOrDefault("baseCurrency", ""))
            .setVenueAccount(variables.getOrDefault("venueAccount", ""))
            .setParentOrderId(variables.getOrDefault("parentOrderId", ""))
            .setRootOrderId(variables.getOrDefault("rootOrderId", ""))
            .setUnderlyingExecutionId(variables.getOrDefault("underlyingExecutionId", ""))
            .setRootExecutionId(variables.getOrDefault("rootExecutionId", ""))
            .setExecutionId(variables.getOrDefault("executionId", ""))
            .setMetadata(
                Metadata.newBuilder()
                    .setCreatedAt(variables.getOrDefault("createdAt", ""))
                    .setUpdatedAt(variables.getOrDefault("updatedAt", ""))
                    .build()
            )
            .setDateTime(variables.getOrDefault("dateTime", ""))
            .build();
        TransactionEntity transaction = StreetCashTradeMapper.mapToTransactionEntity(streetCashTrade, null);
        long tId = transactionRepository.save(transaction);
        SharedTestState.addStreetCashTrade(transaction.transactionUuid(), streetCashTrade);
    }

    @Given("Assume transaction fees for transaction: {string}")
    public void transactionFees(String transactionUuid, DataTable dataTable) {

        List<Fee> fees = dataTable.asMaps().stream()
            .map(row -> Fee.newBuilder()
                .setAmount(row.get("amount"))
                .setFeeType(FeeType.EXCHANGE_FEE)
                .setCurrency(row.get("currency"))
                .build())
            .toList();

        UUID uuid = UUID.fromString(transactionUuid);

        List<TransactionFeeEntity> transactionFeeEntities = FeeMapper.mapToTransactionFeesEntity(fees, uuid);
        transactionFeeEntities
            .forEach(transactionFeeRepository::save);
    }

}
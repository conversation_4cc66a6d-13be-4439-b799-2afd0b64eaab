package io.wyden.settlement.server.settlement.transaction;

import io.wyden.published.booking.TransactionType;
import io.wyden.settlement.server.settlement.PostgresTestcontainer;
import io.wyden.settlement.server.settlement.TestContainersIntegrationBase;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.JdbcTest;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

class TransactionFeeRepositoryIntegrationTest extends TestContainersIntegrationBase {

    @Autowired
    private TransactionFeeRepository transactionFeeRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        // Clean up before each test
        transactionFeeRepository.clear();
        jdbcTemplate.update("DELETE FROM transactions");
    }

    @Test
    void shouldFindFeesByTransactionUuid() {
        // Given
        UUID transactionUuid = createTestTransaction(null);

        TransactionFeeEntity fee1 = TransactionFeeEntity.builder()
            .transactionUuid(transactionUuid)
            .transactionFeeAmount(new BigDecimal("10.50"))
            .transactionFeeCurrency("USD")
            .transactionFeeType("EXCHANGE_FEE")
            .build();

        TransactionFeeEntity fee2 = TransactionFeeEntity.builder()
            .transactionUuid(transactionUuid)
            .transactionFeeAmount(new BigDecimal("5.25"))
            .transactionFeeCurrency("EUR")
            .transactionFeeType("NETWORK_FEE")
            .build();

        // When
        transactionFeeRepository.save(fee1);
        transactionFeeRepository.save(fee2);
        List<TransactionFeeEntity> foundFees = transactionFeeRepository.findByTransactionUuid(transactionUuid);

        // Then
        assertEquals(2, foundFees.size());
        
        // Verify first fee
        TransactionFeeEntity foundFee1 = foundFees.stream()
            .filter(fee -> "USD".equals(fee.transactionFeeCurrency()))
            .findFirst()
            .orElseThrow();
        assertEquals(transactionUuid, foundFee1.transactionUuid());
        assertEquals(0, new BigDecimal("10.50").compareTo(foundFee1.transactionFeeAmount()));
        assertEquals("USD", foundFee1.transactionFeeCurrency());
        assertEquals("EXCHANGE_FEE", foundFee1.transactionFeeType());

        // Verify second fee
        TransactionFeeEntity foundFee2 = foundFees.stream()
            .filter(fee -> "EUR".equals(fee.transactionFeeCurrency()))
            .findFirst()
            .orElseThrow();
        assertEquals(transactionUuid, foundFee2.transactionUuid());
        assertEquals(0, new BigDecimal("5.25").compareTo(foundFee2.transactionFeeAmount()));
        assertEquals("EUR", foundFee2.transactionFeeCurrency());
        assertEquals("NETWORK_FEE", foundFee2.transactionFeeType());
    }

    private UUID createTestTransaction(Long settlementRunId) {
        return createTestTransactionWithTimestamp(settlementRunId, Timestamp.from(Instant.now()));
    }

    private UUID createTestTransactionWithTimestamp(Long settlementRunId, Timestamp timestamp) {
        UUID transactionUuid = UUID.randomUUID();
        TransactionEntity transaction = TransactionEntity.builder()
            .orderId(UUID.randomUUID())
            .transactionType(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE)
            .portfolioId(UUID.randomUUID())
            .transactionUuid(transactionUuid)
            .createdAt(timestamp)
            .updatedAt(timestamp)
            .transactionDatetime(timestamp)
            .venueExecutionId(UUID.randomUUID())
            .description("Test transaction")
            .quantity(new BigDecimal("100.00"))
            .leavesQuantity(new BigDecimal("0.00"))
            .price(new BigDecimal("50.00"))
            .currency("BTC")
            .intOrderId(UUID.randomUUID())
            .extOrderId(UUID.randomUUID())
            .baseCurrency("USD")
            .venueAccount("test-venue")
            .parentOrderId(UUID.randomUUID())
            .rootOrderId(UUID.randomUUID())
            .rootExecutionId(UUID.randomUUID())
            .executionId(UUID.randomUUID())
            .settlementRunId(settlementRunId)
            .settlementDate(null)
            .build();

        transactionRepository.save(transaction);
        return transactionUuid;
    }
}

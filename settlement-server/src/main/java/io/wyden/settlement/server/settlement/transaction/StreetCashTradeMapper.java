package io.wyden.settlement.server.settlement.transaction;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.settlement.client.transaction.SettlementStreetCashTrade;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static io.wyden.published.booking.TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Component
public class StreetCashTradeMapper {

    private final VenueAccountCacheFacade venueAccountCacheFacade;
    private final PortfoliosCacheFacade portfoliosCacheFacade;
    private final TransactionFeeRepository transactionFeeRepository;

    public StreetCashTradeMapper(VenueAccountCacheFacade venueAccountCacheFacade,
                                PortfoliosCacheFacade portfoliosCacheFacade,
                                TransactionFeeRepository transactionFeeRepository) {
        this.venueAccountCacheFacade = venueAccountCacheFacade;
        this.portfoliosCacheFacade = portfoliosCacheFacade;
        this.transactionFeeRepository = transactionFeeRepository;
    }

    public SettlementStreetCashTrade mapToDto(TransactionEntity entity) {

        Optional<TransactionFeeEntity> fee = transactionFeeRepository.findByTransactionUuid(entity.transactionUuid())
            .stream()
            .filter(fee1 -> fee1.transactionFeeType().equals("EXCHANGE_FEE"))
            .findFirst();

        String venueAccountId = entity.venueAccount();
        return SettlementStreetCashTrade.builder()
            .id(entity.id().toString())
            .selected(nonNull(entity.settlementRunId()))
            .dateTime(Objects.toString(entity.transactionDatetime(), ""))
            .uuid(Objects.toString(entity.transactionUuid(), ""))
            .executionId(Objects.toString(entity.executionId(), ""))
            .venueExecutionId(Objects.toString(entity.venueExecutionId(), ""))
            .description(entity.description())
            .quantity(entity.quantity() != null ? entity.quantity().floatValue() : 0f)
            .price(entity.price() != null ? entity.price().floatValue() : 0f)
            .currency(entity.currency())
            .intOrderId(Objects.toString(entity.intOrderId(), ""))
            .extOrderId(Objects.toString(entity.extOrderId(), ""))
            .settled(entity.settlementDate() != null ? "true" : "false")
            .settlementRunId(entity.settlementRunId() != null ? entity.settlementRunId().toString() : null)
            .settledDateTime(entity.settlementDate() != null
                ? DateUtils.sqlTimestampToEpochMillis(entity.settlementDate())
                : null)
            .orderId(Objects.toString(entity.orderId(), ""))
            .portfolioId(Objects.toString(entity.portfolioId(), ""))
            .portfolioName(isNull(entity.portfolioId()) ? ""
                : portfoliosCacheFacade.find(entity.portfolioId().toString()).map(Portfolio::getName).orElse("Portfolio Name not found"))
            .baseCurrency(entity.baseCurrency())
            .venueAccount(venueAccountId)
            .venueAccountName(
                isBlank(venueAccountId) ? "" :
                venueAccountCacheFacade.find(venueAccountId).map(VenueAccount::getVenueAccountName).orElse("Venue Account Name not found")
            )
            .rootExecutionId(Objects.toString(entity.rootExecutionId(), ""))
            .rootOrderId(Objects.toString(entity.rootOrderId(), ""))
            .underlyingExecutionId("")
            .parentOrderId(Objects.toString(entity.parentOrderId(), ""))
            .feeAmount(fee.map(TransactionFeeEntity::transactionFeeAmount).orElse(BigDecimal.ZERO))
            .feeCurrency(fee.map(TransactionFeeEntity::transactionFeeCurrency).orElse(""))
            .build();
    }

    public static TransactionEntity mapToTransactionEntity(StreetCashTradeSnapshot proto, Long settlementRunId) {
        UUID orderId = parseUUID(proto.getOrderId());
        UUID portfolioId = parseUUID(proto.getPortfolio());
        UUID transactionUuid = parseUUID(proto.getUuid());
        UUID venueExecutionId = parseUUID(proto.getVenueExecutionId());
        UUID intOrderId = parseUUID(proto.getIntOrderId());
        UUID extOrderId = parseUUID(proto.getExtOrderId());
        UUID parentOrderId = parseUUID(proto.getParentOrderId());
        UUID rootOrderId = parseUUID(proto.getRootOrderId());
        UUID underlyingExecutionId = parseUUID(proto.getUnderlyingExecutionId());
        UUID rootExecutionId = parseUUID(proto.getRootExecutionId());
        UUID executionId = parseUUID(proto.getExecutionId());

        String clientSettlementRunId = proto.getClientSettlementId();

        BigDecimal quantity = parseBigDecimal(proto.getQuantity());
        BigDecimal leavesQuantity = parseBigDecimal(proto.getLeavesQuantity());
        BigDecimal price = parseBigDecimal(proto.getPrice());


        return new TransactionEntity(
            null,
            orderId,
            TRANSACTION_TYPE_STREET_CASH_TRADE,
            portfolioId,
            transactionUuid,
            parseTimestamp(proto.getMetadata().getCreatedAt()),
            parseTimestamp(proto.getMetadata().getUpdatedAt()),
            parseTimestamp(proto.getDateTime()),
            venueExecutionId,
            proto.getDescription(),
            quantity,
            leavesQuantity,
            price,
            proto.getCurrency(),
            intOrderId,
            extOrderId,
            proto.getBaseCurrency(),
            proto.getVenueAccount(),
            null,
            parentOrderId,
            rootOrderId,
            rootExecutionId,
            executionId,
            settlementRunId,
            clientSettlementRunId,
            null
        );
    }

    private static UUID parseUUID(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }
        try {
            return UUID.fromString(str);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    private static BigDecimal parseBigDecimal(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }
        try {
            return new BigDecimal(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private static Timestamp parseTimestamp(String isoDatetime) {
        if (isoDatetime == null || isoDatetime.isEmpty()) {
            return null;
        }
        try {
            return Timestamp.from(Instant.parse(isoDatetime));
        } catch (Exception e) {
            return null;
        }
    }
}
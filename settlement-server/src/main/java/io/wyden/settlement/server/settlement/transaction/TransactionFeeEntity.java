package io.wyden.settlement.server.settlement.transaction;

import java.math.BigDecimal;
import java.util.UUID;

public record TransactionFeeEntity(Long id,
                                   UUID transactionUuid,
                                   BigDecimal transactionFeeAmount,
                                   String transactionFeeCurrency,
                                   String transactionFeeType) {

    public static Builder builder() {
        return new Builder();
    }

    public Builder toBuilder() {
        return new Builder()
            .id(this.id)
            .transactionUuid(this.transactionUuid)
            .transactionFeeAmount(this.transactionFeeAmount)
            .transactionFeeCurrency(this.transactionFeeCurrency)
            .transactionFeeType(this.transactionFeeType);
    }

    public static class Builder {
        private Long id;
        private UUID transactionUuid;
        private BigDecimal transactionFeeAmount;
        private String transactionFeeCurrency;
        private String transactionFeeType;

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder transactionUuid(UUID transactionUuid) {
            this.transactionUuid = transactionUuid;
            return this;
        }

        public Builder transactionFeeAmount(BigDecimal transactionFeeAmount) {
            this.transactionFeeAmount = transactionFeeAmount;
            return this;
        }

        public Builder transactionFeeCurrency(String transactionFeeCurrency) {
            this.transactionFeeCurrency = transactionFeeCurrency;
            return this;
        }

        public Builder transactionFeeType(String transactionFeeType) {
            this.transactionFeeType = transactionFeeType;
            return this;
        }

        public TransactionFeeEntity build() {
            return new TransactionFeeEntity(
                id,
                transactionUuid,
                transactionFeeAmount,
                transactionFeeCurrency,
                transactionFeeType
            );
        }
    }
}
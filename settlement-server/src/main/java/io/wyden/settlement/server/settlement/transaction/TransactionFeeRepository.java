package io.wyden.settlement.server.settlement.transaction;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public class TransactionFeeRepository {
    private final JdbcTemplate jdbcTemplate;

    public TransactionFeeRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public void save(TransactionFeeEntity transactionFee) {
        jdbcTemplate.update(
            "INSERT INTO transaction_fee (transaction_uuid, transaction_fee_amount, " +
            "transaction_fee_currency, transaction_fee_type) " +
            "VALUES (?, ?, ?, ?)",
            transactionFee.transactionUuid(),
            transactionFee.transactionFeeAmount(),
            transactionFee.transactionFeeCurrency(),
            transactionFee.transactionFeeType()
        );
    }

    public List<TransactionFeeEntity> findByTransactionUuid(UUID transactionUuid) {
        String sql = """
            SELECT id, transaction_uuid, transaction_fee_amount, transaction_fee_currency, transaction_fee_type
            FROM transaction_fee
            WHERE transaction_uuid = ?
            """;

        return jdbcTemplate.query(sql, (rs, rowNum) -> new TransactionFeeEntity(
            rs.getLong("id"),
            UUID.fromString(rs.getString("transaction_uuid")),
            rs.getBigDecimal("transaction_fee_amount"),
            rs.getString("transaction_fee_currency"),
            rs.getString("transaction_fee_type")
        ), transactionUuid);
    }

    public List<CurrencyAmount> findCurrencyAmount(long settlementRunId) {

        String sql = """
            SELECT t.venue_account as ACCOUNT_ID, tf.transaction_fee_currency as CURRENCY, SUM(tf.transaction_fee_amount) as AMOUNT
            FROM transactions t
            JOIN transaction_fee tf ON t.transaction_uuid = tf.transaction_uuid
            WHERE t.settlement_run_id = :runId
            GROUP BY tf.transaction_fee_currency, t.venue_account
            """;

        MapSqlParameterSource params = new MapSqlParameterSource()
            .addValue("runId", settlementRunId);

        return new NamedParameterJdbcTemplate(jdbcTemplate).query(
            sql,
            params,
            (rs, rowNum) -> new CurrencyAmount(
                rs.getString("ACCOUNT_ID"),
                rs.getString("CURRENCY"),
                rs.getBigDecimal("AMOUNT")
            )
        );
    }

    public void saveAll(List<TransactionFeeEntity> transactionFees) {
        if (transactionFees.isEmpty()) {
            return;
        }

        String sql = """
            INSERT INTO transaction_fee (transaction_uuid, transaction_fee_amount,
            transaction_fee_currency, transaction_fee_type)
            VALUES (?, ?, ?, ?)
            """;

        jdbcTemplate.batchUpdate(sql, transactionFees, transactionFees.size(), (ps, fee) -> {
            ps.setObject(1, fee.transactionUuid());
            ps.setBigDecimal(2, fee.transactionFeeAmount());
            ps.setString(3, fee.transactionFeeCurrency());
            ps.setString(4, fee.transactionFeeType());
        });
    }

    public void clear() {
        jdbcTemplate.update("delete from transaction_fee");
    }
}
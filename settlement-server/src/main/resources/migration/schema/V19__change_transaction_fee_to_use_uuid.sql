-- Change transaction_fee table to use transaction_uuid instead of transaction_id
-- This allows linking fees to transactions using UUID instead of database ID

-- Step 1: Add new transaction_uuid column
ALTER TABLE transaction_fee 
    ADD COLUMN transaction_uuid UUID;

-- Step 2: Populate transaction_uuid from existing transaction_id relationships
UPDATE transaction_fee tf 
SET transaction_uuid = t.transaction_uuid 
FROM transactions t 
WHERE tf.transaction_id = t.id;

-- Step 3: Make transaction_uuid NOT NULL (all rows should now have values)
ALTER TABLE transaction_fee 
    ALTER COLUMN transaction_uuid SET NOT NULL;

-- Step 4: Drop old foreign key constraint
ALTER TABLE transaction_fee 
    DROP CONSTRAINT IF EXISTS fk_transaction_id;

-- Step 5: Drop old transaction_id column
ALTER TABLE transaction_fee 
    DROP COLUMN transaction_id;

-- Step 6: Add new foreign key constraint using transaction_uuid
ALTER TABLE transaction_fee 
    ADD CONSTRAINT fk_transaction_uuid 
        FOREIGN KEY (transaction_uuid) 
            REFERENCES transactions (transaction_uuid) 
            ON DELETE CASCADE;

-- Step 7: Create index for performance
CREATE INDEX idx_transaction_fee_transaction_uuid ON transaction_fee (transaction_uuid);
